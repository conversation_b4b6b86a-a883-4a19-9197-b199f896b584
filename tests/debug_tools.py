#!/usr/bin/env python3
"""
Debug script để test các tool handlers trự<PERSON> tiếp với <PERSON>otViet API thật.
Không dùng mock - gọi thẳng vào API để debug.

Usage:
    python debug_tools.py
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path để import được modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from albatross_kiotviet_mcp.config import get_api_client, get_settings
from albatross_kiotviet_mcp.tools import calculate_daily_revenue_tool, get_categories_tool, get_invoices_by_day_tool


async def debug_categories():
    """Debug categories tool với API thật."""
    print("🔧 Testing Categories Tool...")
    print("-" * 50)
    
    try:
        api_client = get_api_client()
        async with api_client:
            # Test với parameters đơn giản
            result = await get_categories_tool.get_categories(
                api_client=api_client,
                page_size=5,  # Lấy ít để debug
                current_item=0,
                order_direction="Asc",
                hierarchical_data=False
            )
            
            print(f"✅ Categories retrieved: {result['total']} total")
            print(f"📄 Page size: {result['pageSize']}")
            print(f"📊 Data count: {len(result['data'])}")
            
            # In ra 2 categories đầu để xem structure
            if result['data']:
                print("\n📋 Sample categories:")
                for i, category in enumerate(result['data'][:2]):
                    print(f"  {i+1}. {category['categoryName']} (ID: {category['id']})")
            
            return True
            
    except Exception as e:
        print(f"❌ Error in categories tool: {e}")
        print(f"🔍 Error type: {type(e).__name__}")
        return False


async def debug_invoices():
    """Debug invoices tool với API thật."""
    print("\n🔧 Testing Invoices Tool...")
    print("-" * 50)
    
    try:
        api_client = get_api_client()
        async with api_client:
            # Test với date range gần đây
            to_date = datetime.now()
            from_date = to_date - timedelta(days=7)  # 7 ngày trước
            
            result = await get_invoices_by_day_tool.get_invoices_by_day(
                api_client=api_client,
                from_date=from_date.strftime("%Y-%m-%d"),
                to_date=to_date.strftime("%Y-%m-%d"),
                page_size=5,  # Lấy ít để debug
                current_item=0,
                order_direction="Desc"  # Mới nhất trước
            )
            
            print(f"✅ Invoices retrieved: {result['total']} total")
            print(f"📄 Page size: {result['pageSize']}")
            print(f"📊 Data count: {len(result['data'])}")
            print(f"📅 Date range: {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}")
            
            # In ra 2 invoices đầu để xem structure
            if result['data']:
                print("\n📋 Sample invoices:")
                for i, invoice in enumerate(result['data'][:2]):
                    print(f"  {i+1}. {invoice['code']} - {invoice.get('total', 0):,.0f} VND")
                    if invoice.get('customerName'):
                        print(f"      Customer: {invoice['customerName']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error in invoices tool: {e}")
        print(f"🔍 Error type: {type(e).__name__}")
        return False


async def debug_revenue():
    """Debug revenue tool với API thật."""
    print("\n🔧 Testing Revenue Tool...")
    print("-" * 50)
    
    try:
        api_client = get_api_client()
        async with api_client:
            # Test với date range ngắn
            to_date = datetime.now()
            from_date = to_date - timedelta(days=3)  # 3 ngày trước
            
            result = await calculate_daily_revenue_tool.calculate_daily_revenue(
                api_client=api_client,
                from_date=from_date.strftime("%Y-%m-%d"),
                to_date=to_date.strftime("%Y-%m-%d"),
                include_details=False  # Không lấy details để nhanh hơn
            )
            
            summary = result['summary']
            print(f"✅ Revenue calculated successfully")
            print(f"💰 Total Revenue: {summary['total_revenue']:,.0f} VND")
            print(f"📊 Total Invoices: {summary['total_invoices']}")
            print(f"📈 Average Invoice: {summary['average_invoice_value']:,.0f} VND")
            print(f"📅 Date Range: {summary['date_range']['from']} to {summary['date_range']['to']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error in revenue tool: {e}")
        print(f"🔍 Error type: {type(e).__name__}")
        return False


async def debug_validation():
    """Debug validation functions."""
    print("\n🔧 Testing Validation Functions...")
    print("-" * 50)
    
    try:
        # Test pagination validation
        print("📋 Testing pagination validation:")
        get_categories_tool.validate_pagination_params(50, 0)
        print("  ✅ Valid params (50, 0) - OK")
        
        try:
            get_categories_tool.validate_pagination_params(0, 0)
            print("  ❌ Should have failed for page_size=0")
        except ValueError as e:
            print(f"  ✅ Correctly rejected page_size=0: {e}")
        
        # Test order direction validation
        print("\n📋 Testing order direction validation:")
        get_categories_tool.validate_order_direction("Asc")
        print("  ✅ Valid direction 'Asc' - OK")
        
        try:
            get_categories_tool.validate_order_direction("Invalid")
            print("  ❌ Should have failed for 'Invalid'")
        except ValueError as e:
            print(f"  ✅ Correctly rejected 'Invalid': {e}")
        
        # Test date parsing
        print("\n📋 Testing date parsing:")
        from_date, to_date = get_invoices_by_day_tool.parse_and_validate_dates("2025-01-01", "2025-01-31")
        print(f"  ✅ Date parsing OK: {from_date} to {to_date}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in validation: {e}")
        return False


def check_environment():
    """Kiểm tra môi trường trước khi chạy test."""
    print("🔍 Checking Environment...")
    print("-" * 50)
    
    try:
        settings = get_settings()
        
        # Check required settings
        required_fields = ['kiotviet_client_id', 'kiotviet_client_secret', 'kiotviet_retailer']
        missing_fields = []
        
        for field in required_fields:
            value = getattr(settings, field, None)
            if not value or value.startswith('your_'):
                missing_fields.append(field)
        
        if missing_fields:
            print("❌ Missing required configuration:")
            for field in missing_fields:
                print(f"   - {field}")
            print("\n💡 Please update your .env file with actual KiotViet credentials")
            return False
        
        print("✅ Configuration looks good")
        print(f"🏪 Retailer: {settings.kiotviet_retailer}")
        print(f"🔗 API URL: {settings.kiotviet_api_base_url}")
        return True
        
    except Exception as e:
        print(f"❌ Environment check failed: {e}")
        return False


async def main():
    """Main debug function."""
    print("🚀 KiotViet MCP Tools Debug Script")
    print("=" * 60)
    
    # Check environment first
    if not check_environment():
        print("\n❌ Environment check failed. Please fix configuration first.")
        return
    
    print("\n🎯 Starting API tests with real KiotViet API...")
    
    # Run tests
    results = []
    
    # Test validation functions (không cần API)
    results.append(await debug_validation())
    
    # Test API functions
    results.append(await debug_categories())
    results.append(await debug_invoices())
    results.append(await debug_revenue())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Debug Results Summary:")
    print("-" * 30)
    
    test_names = ["Validation", "Categories", "Invoices", "Revenue"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{name:12} : {status}")
    
    total_passed = sum(results)
    print(f"\nTotal: {total_passed}/{len(results)} tests passed")
    
    if total_passed == len(results):
        print("🎉 All tests passed! Tools are working correctly.")
    else:
        print("⚠️  Some tests failed. Check the errors above.")


if __name__ == "__main__":
    # Run the debug script
    asyncio.run( main())
