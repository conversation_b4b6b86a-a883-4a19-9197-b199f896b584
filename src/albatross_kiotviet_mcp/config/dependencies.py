"""Simple dependency management for KiotViet MCP Server.

This module provides cached instances of settings and API client to avoid
recreating them multiple times. The caching is simple and effective.
"""

from functools import lru_cache
from .config import Settings
from ..api import KiotVietAPIClient


# @lru_cache(maxsize=1)
def get_settings() -> Settings:
    """Get cached settings instance.

    Returns:
        Settings: Cached configuration instance
    """
    return Settings()


# @lru_cache(maxsize=1)
def get_api_client() -> KiotVietAPIClient:
    """Get cached API client instance.

    Returns:
        KiotVietAPIClient: Cached API client instance with settings
    """
    return KiotVietAPIClient(get_settings())
